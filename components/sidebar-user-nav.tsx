'use client';

import { ChevronUp } from 'lucide-react';
import Image from 'next/image';
import type { User } from 'next-auth';
import { signOut, useSession } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { useState } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useRouter, usePathname } from 'next/navigation';
import { toast } from './toast';
import { LoaderIcon } from './icons';
import { SettingsModal } from './settings-modal';
import { LanguageSwitcher } from './language-switcher';
import { useTranslations } from 'next-intl';
import { FirstMessageModal } from './first-message-modal';
export function SidebarUserNav({ user }: { user: User }) {
  const router = useRouter();
  const pathname = usePathname();
  const { data, status } = useSession();
  const { setTheme, theme } = useTheme();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [showHowToModal, setShowHowToModal] = useState(false);
  const t = useTranslations('sidebar.userNav');

  const isAdmin = data?.user?.type === 'admin';
  const isInAdminArea = pathname?.startsWith('/admin');

  return (
    <SidebarMenu className="select-none">
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            {status === 'loading' ? (
              <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10 justify-between">
                <div className="flex flex-row gap-2">
                  <div className="size-6 bg-zinc-500/30 rounded-full animate-pulse" />
                  <span className="bg-zinc-500/30 text-transparent rounded-md animate-pulse">
                    Loading auth status
                  </span>
                </div>
                <div className="animate-spin text-zinc-500">
                  <LoaderIcon />
                </div>
              </SidebarMenuButton>
            ) : (
              <SidebarMenuButton
                data-testid="user-nav-button"
                className="data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10"
              >
                <Image
                  src={`https://avatar.vercel.sh/${user.email}`}
                  alt={user.email ?? 'User Avatar'}
                  width={24}
                  height={24}
                  className="rounded-full"
                />
                <span data-testid="user-email" className="truncate">
                  {user?.type === 'admin' ? `Admin: ${user?.email}` : user?.email}
                </span>
                <ChevronUp className="ml-auto" />
              </SidebarMenuButton>
            )}
          </DropdownMenuTrigger>
          <DropdownMenuContent
            data-testid="user-nav-menu"
            side="top"
            className="w-[--radix-popper-anchor-width]"
          >
            <div className="p-2 flex items-center justify-between gap-2">
              <div className="text-sm">{t('language')}</div>
              <LanguageSwitcher />
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              data-testid="user-nav-item-theme"
              className="cursor-pointer"
              onSelect={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            >
              {theme === 'light' ? t('theme.toggleDark') : t('theme.toggleLight')}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              data-testid="user-nav-item-how-to"
              className="cursor-pointer"
              onSelect={() => setShowHowToModal(true)}
            >
              {t('howToWorkWithEnai')}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {isAdmin && (
              <>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onSelect={() => setIsSettingsOpen(true)}
                >
                  {t('settings')}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {isInAdminArea ? (
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onSelect={() => router.push('/')}
                  >
                    Back to Chat
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onSelect={() => router.push('/admin/users')}
                  >
                    Admin Area
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
              </>
            )}
            <DropdownMenuItem asChild data-testid="user-nav-item-auth">
              <button
                type="button"
                className="w-full cursor-pointer"
                onClick={() => {
                  if (status === 'loading') {
                    toast({
                      type: 'error',
                      description: t('auth.checkingStatus'),
                    });

                    return;
                  }

                  signOut({
                    redirectTo: '/',
                  });
                }}
              >
                {t('auth.signOut')}
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>

      {/* Settings Modal */}
      <SettingsModal open={isSettingsOpen} onOpenChange={setIsSettingsOpen} />
      <FirstMessageModal isOpen={showHowToModal} onProceed={() => setShowHowToModal(false)} />
    </SidebarMenu>
  );
}
