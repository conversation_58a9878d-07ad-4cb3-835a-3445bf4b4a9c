
import {
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
} from './ui/sidebar';
import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { renameChat } from '@/app/(chat)/actions';
import {
  CheckCircleFillIcon,
  GlobeIcon,
  LockIcon,
  MoreHorizontalIcon,
  PencilEditIcon,
  ShareIcon,
  TrashIcon,
} from './icons';
import { memo, useState } from 'react';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import { usePathname } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { pl, enUS } from 'date-fns/locale';
import { useTranslations, useLocale } from 'next-intl';
import type { ChatWithFirstMessage } from './sidebar-history';

// Utility function to extract text from message parts
const extractTextFromParts = (parts: any): string => {
  if (!parts || !Array.isArray(parts)) return '';
  
  return parts
    .filter((part: any) => part.type === 'text')
    .map((part: any) => part.text)
    .join(' ')
    .trim();
};

// Utility function to truncate text
const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.slice(0, maxLength)}...`;
};

// Utility function to format model name for display
const formatModelName = (modelId?: string | null): string => {
  if (!modelId) return 'AI Assistant';
  
  // Clean up common model naming patterns
  return modelId
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const PureChatItem = ({
  chat,
  isActive,
  onDelete,
  setOpenMobile,
  onChatRenamed,
}: {
  chat: ChatWithFirstMessage;
  isActive: boolean;
  onDelete: (chatId: string) => void;
  setOpenMobile: (open: boolean) => void;
  onChatRenamed: (updatedChat: ChatWithFirstMessage) => void;
}) => {
  const { visibilityType, setVisibilityType } = useChatVisibility({
    chatId: chat.id,
    initialVisibilityType: chat.visibility,
  });

  // Translations and locale
  const t = useTranslations('sidebar.chatActions');
  const locale = useLocale();

  // Track local active state to immediately highlight on click
  const [localActive, setLocalActive] = useState(isActive);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [newTitle, setNewTitle] = useState(chat.title);
  const pathname = usePathname();

  // Update localActive when isActive (from router) changes
  if (localActive !== isActive) {
    setLocalActive(isActive);
  }

  const handleClick = () => {
    setLocalActive(true);
    setOpenMobile(false);
  };

  const handleRename = async () => {
    try {
      const result = await renameChat({ chatId: chat.id, newTitle });
      if (result) { // Check if renameChat returned a result (updated chat)
        onChatRenamed({ ...chat, ...result } as ChatWithFirstMessage);
      }
      setIsRenameDialogOpen(false);
    } catch (error) {
      // Handle or log the error appropriately if needed
      console.error("Failed to rename chat:", error);
      // Optionally, show a toast notification for the error
      setIsRenameDialogOpen(false); // Still close dialog on error, or keep open for retry
    }
  };

  // Extract prompt text and model info from first message
  const promptText = chat.firstMessage 
    ? extractTextFromParts(chat.firstMessage.parts)
    : '';
  const modelName = formatModelName(chat.firstMessage?.modelId);
  const promptName = chat.firstMessage?.promptName;
  
  // Get proper locale for date formatting
  const dateLocale = locale === 'pl' ? pl : enUS;
  const relativeTime = formatDistanceToNow(new Date(chat.createdAt), { 
    addSuffix: true,
    locale: dateLocale
  });

  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild isActive={localActive} className="h-auto py-2">
        <Link href={`/chat/${chat.id}`} onClick={handleClick} className="flex flex-col items-start gap-1 text-left">
          <div className="flex items-center justify-between w-full">
            <span className="font-medium truncate flex-1">{chat.title}</span>
            <span className="text-xs text-sidebar-foreground/50 ml-2 shrink-0">
              {relativeTime}
            </span>
          </div>
          
          {/* Prompt text preview */}
          {promptText && (
            <div className="text-xs text-sidebar-foreground/70 line-clamp-2 w-full -mt-1">
              {truncateText(promptText, 120)}
            </div>
          )}
          
          {/* Model and Prompt badges */}
          <div className="flex gap-1 mt-1 w-full">
            {/* Model badge */}
            <div className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-sidebar-accent/40 text-sidebar-foreground/80 border border-sidebar-border/50">
              {modelName}
            </div>
            
            {/* Prompt name badge */}
            {promptName && (
              <div className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-primary/10 text-stone-500 border border-primary/20 max-w-32">
                <span className="truncate">{promptName}</span>
              </div>
            )}
          </div>
        </Link>
      </SidebarMenuButton>

      <DropdownMenu modal={true}>
        <DropdownMenuTrigger asChild>
          <SidebarMenuAction
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground mr-0.5"
            showOnHover={!localActive}
          >
            <MoreHorizontalIcon />
            <span className="sr-only">More</span>
          </SidebarMenuAction>
        </DropdownMenuTrigger>

        <DropdownMenuContent side="bottom" align="end">
          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="cursor-pointer">
              <ShareIcon />
              <span>{t('share')}</span>
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuItem
                  className="cursor-pointer flex-row justify-between"
                  onClick={() => {
                    setVisibilityType('private');
                  }}
                >
                  <div className="flex flex-row gap-2 items-center">
                    <LockIcon size={12} />
                    <span>{t('private')}</span>
                  </div>
                  {visibilityType === 'private' ? (
                    <CheckCircleFillIcon />
                  ) : null}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="cursor-pointer flex-row justify-between"
                  onClick={() => {
                    setVisibilityType('public');
                  }}
                >
                  <div className="flex flex-row gap-2 items-center">
                    <GlobeIcon />
                    <span>{t('public')}</span>
                  </div>
                  {visibilityType === 'public' ? <CheckCircleFillIcon /> : null}
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
          <DropdownMenuItem
            className="cursor-pointer"
            onSelect={() => {
              setNewTitle(chat.title); // Reset title to current chat title when opening dialog
              setIsRenameDialogOpen(true);
            }}
          >
            <PencilEditIcon />
            <span>{t('rename')}</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
            onSelect={() => onDelete(chat.id)}
          >
            <TrashIcon />
            <span>{t('delete')}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('renameDialog.title')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('renameDialog.description')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <Input
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleRename();
              }
            }}
            placeholder={t('renameDialog.placeholder')}
          />
          <AlertDialogFooter>
            <AlertDialogCancel>{t('renameDialog.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleRename}>{t('renameDialog.confirm')}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </SidebarMenuItem>
  );
};

export const ChatItem = memo(PureChatItem, (prevProps, nextProps) => {
  // Only re-render if isActive or chat.title changes
  if (
    prevProps.isActive !== nextProps.isActive ||
    prevProps.chat.title !== nextProps.chat.title ||
    prevProps.chat.visibility !== nextProps.chat.visibility
  ) {
    return false;
  }
  return true;
});
