import { memo } from 'react';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { SpeakerIcon, LoaderIcon, StopIcon } from './icons';
import { useTTS } from '@/hooks/use-tts';
import { useTranslations } from 'next-intl';

interface TTSButtonProps {
  text: string;
  className?: string;
}

function PureTTSButton({ text, className }: TTSButtonProps) {
  const { isLoading, isPlaying, speak, stop } = useTTS();
  const t = useTranslations('tts');

  const handleClick = () => {
    if (isPlaying) {
      stop();
    } else {
      speak(text);
    }
  };

  const getIcon = () => {
    if (isLoading) {
      return <LoaderIcon />;
    }
    if (isPlaying) {
      return <StopIcon />;
    }
    return <SpeakerIcon />;
  };

  const getTooltipText = () => {
    if (isLoading) {
      return t('generating');
    }
    if (isPlaying) {
      return t('stop');
    }
    return t('readAloud');
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className={`py-1 px-2 h-fit text-muted-foreground ${className || ''}`}
          variant="outline"
          onClick={handleClick}
          disabled={isLoading || !text.trim()}
        >
          {getIcon()}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{getTooltipText()}</TooltipContent>
    </Tooltip>
  );
}

export const TTSButton = memo(PureTTSButton);