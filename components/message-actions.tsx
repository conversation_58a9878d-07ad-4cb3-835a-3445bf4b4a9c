import type { TextPart, UIMessage } from 'ai';
import useSWR, { useSWRConfig } from 'swr';
import { useCopyToClipboard } from 'usehooks-ts';

import type { Vote } from '@/lib/db/schema';
import { deleteTrailingMessages } from '@/app/(chat)/actions';

import {
  CopyIcon,
  ThumbDownIcon,
  ThumbUpIcon,
  TrashIcon,
  RefreshCcwIcon,
  InfoIcon,
  MessageIcon,
  SpeakerIcon,
  LoaderIcon,
  StopIcon,
} from './icons';
import { Button } from './ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';
import { memo, useState } from 'react';
import equal from 'fast-deep-equal';
import { toast } from 'sonner';
import type { UseChatHelpers } from '@ai-sdk/react';
import { chatModels } from '@/lib/ai/models';
import { usePrompts } from '@/hooks/use-prompts';
import { useAppState } from '@/hooks/use-app-state';
import { useTTS } from '@/hooks/use-tts';
import { useSubPrompt } from '@/hooks/use-sub-prompt';

interface PureMessageActionsProps {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  allMessages: UIMessage[];
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  onOpenComments?: () => void;
  isOwner?: boolean;
  isAdmin?: boolean;
}

export function PureMessageActions({
  chatId,
  message,
  vote,
  isLoading,
  allMessages,
  setMessages,
  reload,
  onOpenComments,
  isOwner = true,
  isAdmin = false,
}: PureMessageActionsProps) {
  const { mutate } = useSWRConfig();
  const [_, copyToClipboard] = useCopyToClipboard();
  const [isRegenerating, setIsRegenerating] = useState(false);
  const { prompts } = usePrompts();
  const { selectedPrompt } = useAppState();
  const { speak, stop, isMessagePlaying, isMessageLoading } = useTTS();

  // Extract subPromptId from message annotations
  const annotations = message.annotations?.[0] as any;
  const subPromptId = annotations?.subPromptId;

  // Use the new hook to fetch sub-prompt data with automatic caching and deduplication
  const { subPromptName, isLoading: isLoadingSubPrompt } = useSubPrompt(subPromptId);

  // Check TTS state for this specific message
  const isThisMessagePlaying = isMessagePlaying(message.id);
  const isThisMessageLoading = isMessageLoading(message.id);

  // Fetch comment count for this message (only for assistant messages)
  const { data: comments } = useSWR(
    onOpenComments && message.role === 'assistant' ? `/api/comments?messageId=${message.id}` : null,
    async (url) => {
      const response = await fetch(url);
      if (response.ok) {
        return response.json();
      }
      return [];
    },
  );
  const commentCount = comments?.length || 0;

  if (isLoading && !isRegenerating) return null;
  if (message.role === 'user') return null;

  // Helper function to get message metadata for the info tooltip
  const getMessageMetadata = () => {
    const annotations = message.annotations?.[0] as any;
    if (!annotations) return null;

    const modelId = annotations.modelId;
    const promptId = annotations.promptId;
    const subPromptId = annotations.subPromptId;

    // Find model name
    const model = chatModels.find((m) => m.id === modelId);
    const modelName = model?.name || modelId || 'Unknown';

    // Find prompt name
    let promptName = 'Unknown';
    if (promptId) {
      const prompt = prompts.find((p) => p.id === promptId) || selectedPrompt;
      promptName = prompt?.name || 'Unknown';
    }

    // Format creation date
    const createdAt = message.createdAt ? new Date(message.createdAt) : null;
    const formattedDate = createdAt
      ? createdAt.toLocaleString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
      : 'Unknown';

    return {
      createdAt: formattedDate,
      modelName,
      promptName,
      subPromptName: subPromptId
        ? subPromptName || (isLoadingSubPrompt ? 'Loading...' : `ID: ${subPromptId}`)
        : null,
    };
  };

  const metadata = getMessageMetadata();

  return (
    <TooltipProvider delayDuration={0}>
      <div className="flex flex-row gap-1 md:gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={async () => {
                const textFromParts = message.parts
                  ?.filter((part): part is TextPart => part.type === 'text')
                  .map((part) => part.text)
                  .join('\n')
                  .trim();

                if (!textFromParts) {
                  toast.error("There's no text to copy!");
                  return;
                }

                await copyToClipboard(textFromParts);
                toast.success('Copied to clipboard!');
              }}
            >
              <CopyIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={() => {
                const textFromParts = message.parts
                  ?.filter((part): part is TextPart => part.type === 'text')
                  .map((part) => part.text)
                  .join('\n')
                  .trim();

                if (!textFromParts) {
                  toast.error("There's no text to read!");
                  return;
                }

                if (isThisMessagePlaying) {
                  stop();
                } else {
                  speak(textFromParts, message.id);
                }
              }}
              disabled={isThisMessageLoading}
            >
              {isThisMessageLoading ? (
                <div className="animate-spin">
                  <LoaderIcon />
                </div>
              ) : isThisMessagePlaying ? (
                <StopIcon />
              ) : (
                <SpeakerIcon />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {isThisMessageLoading
              ? 'Generating speech...'
              : isThisMessagePlaying
                ? 'Stop speech'
                : 'Read aloud'}
          </TooltipContent>
        </Tooltip>

        {metadata && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="py-1 px-2 h-fit text-muted-foreground"
                variant="outline"
              >
                <InfoIcon />
              </Button>
            </TooltipTrigger>
            <TooltipContent
              className="whitespace-nowrap"
              side="bottom"
              align="start"
            >
              <div className="space-y-1 text-sm">
                <div>
                  <strong>Date:</strong> {metadata.createdAt}
                </div>
                <div>
                  <strong>Model:</strong> {metadata.modelName}
                </div>
                <div>
                  <strong>Prompt:</strong> {metadata.promptName}
                </div>
                {metadata.subPromptName && (
                  <div>
                    <strong>Sub-prompt:</strong> {metadata.subPromptName}
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        )}

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-upvote"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              disabled={vote?.isUpvoted}
              variant="outline"
              onClick={async () => {
                const upvote = fetch('/api/vote', {
                  method: 'PATCH',
                  body: JSON.stringify({
                    chatId,
                    messageId: message.id,
                    type: 'up',
                  }),
                });

                toast.promise(upvote, {
                  loading: 'Upvoting Response...',
                  success: () => {
                    mutate<Array<Vote>>(
                      `/api/vote?chatId=${chatId}`,
                      (currentVotes) => {
                        if (!currentVotes) return [];

                        const votesWithoutCurrent = currentVotes.filter(
                          (vote) => vote.messageId !== message.id,
                        );

                        return [
                          ...votesWithoutCurrent,
                          {
                            chatId,
                            messageId: message.id,
                            isUpvoted: true,
                          },
                        ];
                      },
                      { revalidate: false },
                    );

                    return 'Upvoted Response!';
                  },
                  error: 'Failed to upvote response.',
                });
              }}
            >
              <ThumbUpIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Upvote Response</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-downvote"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              disabled={vote && !vote.isUpvoted}
              onClick={async () => {
                const downvote = fetch('/api/vote', {
                  method: 'PATCH',
                  body: JSON.stringify({
                    chatId,
                    messageId: message.id,
                    type: 'down',
                  }),
                });

                toast.promise(downvote, {
                  loading: 'Downvoting Response...',
                  success: () => {
                    mutate<Array<Vote>>(
                      `/api/vote?chatId=${chatId}`,
                      (currentVotes) => {
                        if (!currentVotes) return [];

                        const votesWithoutCurrent = currentVotes.filter(
                          (vote) => vote.messageId !== message.id,
                        );

                        return [
                          ...votesWithoutCurrent,
                          {
                            chatId,
                            messageId: message.id,
                            isUpvoted: false,
                          },
                        ];
                      },
                      { revalidate: false },
                    );

                    return 'Downvoted Response!';
                  },
                  error: 'Failed to downvote response.',
                });
              }}
            >
              <ThumbDownIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Downvote Response</TooltipContent>
        </Tooltip>

        {isOwner && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                data-testid="message-regenerate"
                className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
                variant="outline"
                disabled={isLoading || isRegenerating}
                onClick={async () => {
                  setIsRegenerating(true);
                  const currentMessageIndex = allMessages.findIndex(
                    (m) => m.id === message.id,
                  );
                  let targetUserMessage: UIMessage | null = null;

                  if (currentMessageIndex > -1) {
                    for (let i = currentMessageIndex - 1; i >= 0; i--) {
                      if (allMessages[i].role === 'user') {
                        targetUserMessage = allMessages[i];
                        break;
                      }
                    }
                  }

                  if (!targetUserMessage) {
                    toast.error(
                      'Could not find a previous user message to regenerate from.',
                    );
                    setIsRegenerating(false);
                    return;
                  }

                  const finalTargetUserMessage = targetUserMessage;

                  const promise = new Promise((resolve, reject) => {
                    (async () => {
                      try {
                        await deleteTrailingMessages({
                          id: finalTargetUserMessage.id,
                        });

                        mutate(
                          `/api/chat/messages?chatId=${chatId}`,
                          (cachedMessages: UIMessage[] | undefined) => {
                            if (!cachedMessages) return [];
                            const userMsgIndex = cachedMessages.findIndex(
                              (m) => m.id === finalTargetUserMessage.id,
                            );
                            if (userMsgIndex === -1) return cachedMessages;
                            return cachedMessages.slice(0, userMsgIndex + 1);
                          },
                          { revalidate: false },
                        );

                        setMessages((currentMsgs) => {
                          const userMsgIndex = currentMsgs.findIndex(
                            (m) => m.id === finalTargetUserMessage.id,
                          );
                          if (userMsgIndex === -1) return currentMsgs;
                          return currentMsgs.slice(0, userMsgIndex + 1);
                        });

                        await reload();
                        resolve('Response regenerated successfully!');
                      } catch (error) {
                        console.error('Regeneration failed:', error);
                        reject(new Error('Failed to regenerate response.'));
                      }
                    })();
                  });

                  toast.promise(promise, {
                    loading: 'Regenerating Response...',
                    success: (msg) => {
                      setIsRegenerating(false);
                      return msg as string;
                    },
                    error: (err) => {
                      setIsRegenerating(false);
                      return err.message;
                    },
                  });
                }}
              >
                <RefreshCcwIcon />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Regenerate Response</TooltipContent>
          </Tooltip>
        )}

        {onOpenComments && isAdmin && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                data-testid="message-comments"
                className={`py-1 px-2 h-fit !pointer-events-auto ${
                  commentCount > 0
                    ? 'text-primary bg-yellow-300/10 border-orange-600/80 hover:bg-primary/15'
                    : 'text-muted-foreground'
                }`}
                variant="outline"
                onClick={onOpenComments}
              >
                <MessageIcon />
                {commentCount > 0 && (
                  <span className="leading-none text-[15px] font-bold">
                    {commentCount}
                  </span>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {commentCount > 0
                ? `View ${commentCount} comment${commentCount === 1 ? '' : 's'}`
                : 'Add comment'}
            </TooltipContent>
          </Tooltip>
        )}

        {isAdmin && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                data-testid="message-delete"
                className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
                variant="outline"
                onClick={async () => {
                  const deleteRequest = fetch(
                    `/api/message?chatId=${chatId}&messageId=${message.id}`,
                    {
                      method: 'DELETE',
                    },
                  );

                  toast.promise(deleteRequest, {
                    loading: 'Deleting Message...',
                    success: () => {
                      mutate(
                        `/api/chat/messages?chatId=${chatId}`,
                        (messages: UIMessage[] | undefined) => {
                          if (!messages) return [];
                          return messages.filter((msg) => msg.id !== message.id);
                        },
                        { revalidate: false },
                      );

                      if (setMessages) {
                        setMessages((prevMessages) =>
                          prevMessages.filter((msg) => msg.id !== message.id),
                        );
                      }

                      mutate(`/api/vote?chatId=${chatId}`);

                      return 'Message Deleted!';
                    },
                    error: 'Failed to delete message.',
                  });
                }}
              >
                <TrashIcon />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Delete Message</TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}

export const MessageActions = memo(
  PureMessageActions,
  (prevProps, nextProps) => {
    if (prevProps.chatId !== nextProps.chatId) return false;
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    if (prevProps.setMessages !== nextProps.setMessages) return false;
    if (prevProps.reload !== nextProps.reload) return false;
    if (!equal(prevProps.allMessages, nextProps.allMessages)) return false;
    if (prevProps.onOpenComments !== nextProps.onOpenComments) return false;
    if (prevProps.isOwner !== nextProps.isOwner) return false;

    return true;
  },
);
