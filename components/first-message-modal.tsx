'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { useTranslations } from 'next-intl';

interface FirstMessageModalProps {
  isOpen: boolean;
  onProceed: () => void;
}

export function FirstMessageModal({ isOpen, onProceed }: FirstMessageModalProps) {
  const t = useTranslations('firstMessage');
  return (
    <Dialog open={isOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
          <DialogDescription className="text-left space-y-4 pt-4">
            <p>
              {t('intro')}
            </p>
            <div>
              <p className="mb-2">
                <strong>{t('psTitle')}</strong> {t('psIntro')}
              </p>
              <ul className="list-disc list-inside space-y-2 text-sm">
                <li>{t('point1')}</li>
                <li>{t('point2')}</li>
                <li>{t('point3')}</li>
              </ul>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onProceed} className="w-full">
            {t('understand')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 