import { memo } from 'react';
import { But<PERSON> } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { VolumeIcon, SpeakerOffIcon } from './icons';
import { useTTS } from '@/hooks/use-tts';
import { useTranslations } from 'next-intl';

interface AutoTTSToggleProps {
  className?: string;
}

function PureAutoTTSToggle({ className }: AutoTTSToggleProps) {
  const { isAutoTTSEnabled, toggleAutoTTS } = useTTS();
  const t = useTranslations('tts');

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          className={`py-1 px-2 h-fit ${className || ''}`}
          variant={isAutoTTSEnabled ? 'default' : 'outline'}
          onClick={toggleAutoTTS}
        >
          {isAutoTTSEnabled ? (
            <span className="flex items-center gap-2">
              <VolumeIcon /> {t('autoOn')}
            </span>
          ) : (
            <span className="flex items-center gap-2">
              <SpeakerOffIcon /> {t('autoOff')}
            </span>
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        {isAutoTTSEnabled ? t('disableAuto') : t('enableAuto')}
      </TooltipContent>
    </Tooltip>
  );
}

export const AutoTTSToggle = memo(PureAutoTTSToggle);
