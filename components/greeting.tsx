import { motion } from 'framer-motion';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

export const Greeting = () => {
  const t = useTranslations('greeting');
  return (
    <div
      key="overview"
      className="max-w-3xl mx-auto md:mt-12 px-8 size-full flex flex-col justify-center"
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ delay: 0.5 }}
        className="flex flex-col md:flex-row md:items-center gap-6 mt-2 mb-4"
      >
        <div className="rounded-2xl hidden md:block">
          <Image
            src="/images/icon.png"
            alt="enai"
            width={150}
            height={150}
            className="object-cover"
          />
        </div>

        <div className="flex flex-col text-center md:text-left">
          <div className="text-2xl font-semibold text-primary mb-3">
            {t('title')}
          </div>

          <div className="text-xl text-muted-foreground text-balance">
            {t('description')}
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ delay: 0.8 }}
        className="hidden md:block mt-8 p-5 rounded-2xl bg-card border border-border shadow-sm"
      >
        <div className="text-sm font-medium text-primary mb-3">
          {t('aboutTitle')} <span className="text-accent font-bold">Enai</span>
        </div>
        <div className="text-sm text-muted-foreground">
          {t('aboutDescription')}
        </div>
      </motion.div>
    </div>
  );
};
