'use client';

import { motion } from 'framer-motion';
import { But<PERSON> } from './ui/button';
import { memo } from 'react';
import type { UseChatHelpers } from '@ai-sdk/react';
import type { VisibilityType } from './visibility-selector';
import { useTranslations } from 'next-intl';

interface SuggestedActionsProps {
  chatId: string;
  append: UseChatHelpers['append'];
  selectedVisibilityType: VisibilityType;
  onSubmit?: (message: any, options?: any) => void;
  onSelectSuggestion?: (text: string) => void;
}

function PureSuggestedActions({
  chatId,
  append,
  selectedVisibilityType,
  onSubmit,
  onSelectSuggestion,
}: SuggestedActionsProps) {
  const t = useTranslations('chat.suggestedActions');

  const suggestedActions = [
    {
      title: t('publicSpeaking.title'),
      label: t('publicSpeaking.label'),
      action: t('publicSpeaking.action'),
    },
    {
      title: t('arachnophobia.title'),
      label: t('arachnophobia.label'),
      action: t('arachnophobia.action'),
    },
    {
      title: t('claustrophobia.title'),
      label: t('claustrophobia.label'),
      action: t('claustrophobia.action'),
    },
    {
      title: t('flyingFear.title'),
      label: t('flyingFear.label'),
      action: t('flyingFear.action'),
    },
  ];

  return (
    <div
      data-testid="suggested-actions"
      className="grid sm:grid-cols-2 gap-2 w-full"
    >
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className={index > 1 ? 'hidden sm:block' : 'block'}
        >
          <Button
            variant="ghost"
            onClick={async (e) => {
              e.preventDefault();
              if (onSelectSuggestion) {
                onSelectSuggestion(suggestedAction.action);
              }
            }}
            className="text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start"
          >
            <span className="font-medium">{suggestedAction.title}</span>
            <span className="text-muted-foreground">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(
  PureSuggestedActions,
  (prevProps, nextProps) => {
    if (prevProps.chatId !== nextProps.chatId) return false;
    if (prevProps.selectedVisibilityType !== nextProps.selectedVisibilityType)
      return false;

    return true;
  },
);
