import Form from 'next/form';
import { useTranslations } from 'next-intl';

import { Input } from './ui/input';
import { Label } from './ui/label';

export function RegisterForm({
  action,
  children,
  defaultEmail = '',
  defaultInviteToken = '',
}: {
  action: NonNullable<
    string | ((formData: FormData) => void | Promise<void>) | undefined
  >;
  children: React.ReactNode;
  defaultEmail?: string;
  defaultInviteToken?: string;
}) {
  const t = useTranslations('auth');
  
  return (
    <Form action={action} className="flex flex-col gap-4 px-4 sm:px-16">
      <div className="flex flex-col gap-2">
        <Label
          htmlFor="email"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          {t('emailAddress')}
        </Label>

        <Input
          id="email"
          name="email"
          className="bg-muted text-md md:text-sm"
          type="email"
          placeholder={t('emailPlaceholder')}
          autoComplete="email"
          required
          autoFocus
          defaultValue={defaultEmail}
        />
      </div>

      <div className="flex flex-col gap-2">
        <Label
          htmlFor="password"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          {t('password')}
        </Label>

        <Input
          id="password"
          name="password"
          className="bg-muted text-md md:text-sm"
          type="password"
          required
        />
      </div>

      <div className="flex flex-col gap-2">
        <Label
          htmlFor="inviteToken"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          Invite Code
        </Label>

        <Input
          id="inviteToken"
          name="inviteToken"
          className="bg-muted text-md md:text-sm"
          type="text"
          placeholder="Enter your invite code"
          required
          defaultValue={defaultInviteToken}
        />
      </div>

      {children}
    </Form>
  );
}
