{"common": {"loading": "Loading...", "error": "Error", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "send": "Send", "copy": "Copy", "share": "Share"}, "chat": {"title": "<PERSON><PERSON> - AI Assistant", "placeholder": "How are you feeling today?", "newChat": "New chat", "welcome": "Welcome to Enai! How can I help you today?", "thinking": "Thinking...", "regenerate": "Regenerate", "stop": "Stop", "suggestedActions": {"publicSpeaking": {"title": "I'm scared of public speaking,", "label": "can you help?", "action": "I'm scared of public speaking, can you help?"}, "arachnophobia": {"title": "I need support getting out of", "label": "arachnophobia.", "action": "I need support getting out of arachnophobia."}, "claustrophobia": {"title": "I don't like tight or closed spaces,", "label": "can you assist?", "action": "I don't like tight or closed spaces, can you assist?"}, "flyingFear": {"title": "I'd love to fly but have a phobia of flying.", "label": "Need your support.", "action": "I'd love to fly but have a phobia of flying. Need your support."}}}, "tts": {"generating": "Generating speech...", "stop": "Stop speech", "readAloud": "Read aloud", "autoOn": "Auto-speech on", "autoOff": "Auto-speech off", "disableAuto": "Disable auto-TTS", "enableAuto": "Enable auto-TTS"}, "visibility": {"private": "Private", "privateDesc": "Only you can access this chat", "public": "Public", "publicDesc": "Anyone with the link can access this chat"}, "sidebar": {"toggle": "Toggle Sidebar", "history": "Chat history", "settings": "Settings", "profile": "Profile", "signOut": "Sign out", "userNav": {"language": "Language", "theme": {"toggleDark": "Toggle dark mode", "toggleLight": "Toggle light mode"}, "settings": "Settings", "auth": {"loginToAccount": "Login to your account", "signOut": "Sign out", "checkingStatus": "Checking authentication status, please try again!"}, "howToWorkWithEnai": "How to work with <PERSON><PERSON>", "guest": "Guest"}, "chatActions": {"share": "Share", "private": "Private", "public": "Public", "rename": "<PERSON><PERSON>", "delete": "Delete", "renameDialog": {"title": "Rename Conversation", "description": "Enter a new title for this conversation.", "placeholder": "Enter new title", "cancel": "Cancel", "confirm": "<PERSON><PERSON>"}}, "timeGroups": {"today": "Today", "yesterday": "Yesterday", "lastWeek": "Last 7 days", "lastMonth": "Last 30 days", "older": "Older than last month"}, "emptyStates": {"loginRequired": "Login to save and revisit previous chats!", "noChats": "Your conversations will appear here once you start chatting!", "loading": "Loading Chats..."}, "deleteDialog": {"title": "Are you absolutely sure?", "description": "This action cannot be undone. This will permanently delete your chat and remove it from our servers.", "cancel": "Cancel", "continue": "Continue"}, "toasts": {"deleting": "Deleting chat...", "deleted": "<PERSON><PERSON> deleted successfully", "deleteFailed": "Failed to delete chat"}}, "auth": {"signIn": "Sign in", "signUp": "Sign up", "email": "Email", "password": "Password", "confirmPassword": "Confirm password", "forgotPassword": "Forgot password?", "resetPassword": "Reset password", "backToLogin": "Back to login", "emailAddress": "Email Address", "emailPlaceholder": "<EMAIL>", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "signInDescription": "Use your email and password to sign in", "signUpDescription": "Create an account with your email and password", "noAccount": "Don't have an account?", "forFree": "for free.", "haveAccount": "Already have an account?", "instead": "instead.", "invalidCredentials": "Invalid credentials!", "validationFailed": "Failed validating your submission!", "accountExists": "Account already exists!", "createAccountFailed": "Failed to create account!", "accountCreated": "Account created successfully!", "resetEmailFailed": "Failed to send reset email. Please try again.", "validEmailRequired": "Please enter a valid email address.", "resetEmailSent": "If an account with this email exists, a password reset link has been sent.", "sendResetLink": "Send Reset Link", "resetPasswordDescription": "Enter your email address and we'll send you a link to reset your password", "invalidResetToken": "Invalid or missing reset token. Please request a new password reset link.", "resetPasswordFailed": "Failed to reset password. Please try again.", "passwordRequirements": "Please ensure passwords match and are at least 8 characters.", "resetLinkExpired": "This reset link has expired. Please request a new one.", "passwordResetSuccess": "Your password has been reset successfully!", "passwordsDoNotMatch": "Passwords do not match.", "linkExpired": "Link Expired", "linkExpiredDescription": "Your password reset link has expired. Please request a new one.", "requestNewResetLink": "Request a new reset link", "setNewPassword": "Set New Password", "setNewPasswordDescription": "Enter your new password below"}, "settings": {"title": "Settings", "model": "AI Model", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System"}, "artifacts": {"code": "Code", "text": "Text", "image": "Image", "sheet": "Sheet", "run": "Run", "download": "Download", "fullscreen": "Fullscreen"}, "greeting": {"title": "Here to help you beat phobias and stress.", "description": "I'm Enai, an AI designed to provide a warm, judgment-free space that is tested by industry professionals and safe to use.", "aboutTitle": "About", "aboutDescription": "I'm built by industry professionals - therapists with years of experience. I'm here to support you while, at the same time, not substituting for human contact. I will help you work through your phobia and show you how you can support yourself with a network of people close to you."}, "firstMessage": {"title": "Your first session", "intro": "Thank you for trusting me with your problem. Let me explain the process for a quick moment. In the beginning we'll name the issue you're coming with, the goal and then move to the subconscious to work through what is underneath.", "psTitle": "PS.", "psIntro": "Few aspects of working with me that can help you in getting the most of our sessions:", "point1": "In our conversations I like to get to the gist of things - asking \"why?\" or making you reflect more on something. If this is too much or you feel we are spinning - just let me know. I might not always notice this.", "point2": "If you need me to be more brief, more expressive or anything else - put it in writing and I'll do my best to adjust to your needs.", "point3": "This is a journey and uncomfortable feelings might arise. If it is too much, you feel overwhelmed and in need of human support please let me know. I will direct you straight to our team where you can schedule a session in 24h.", "understand": "I understand"}}