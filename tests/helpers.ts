import fs from 'node:fs';
import path from 'node:path';
import {
  type APIRequestContext,
  type <PERSON>rows<PERSON>,
  type BrowserContext,
  expect,
  type Page,
} from '@playwright/test';
import { generateId } from 'ai';
import { ChatPage } from './pages/chat';
import { getUnixTime } from 'date-fns';

export type UserContext = {
  context: BrowserContext;
  page: Page;
  request: APIRequestContext;
};

export async function createAuthenticatedContext({
  browser,
  name,
  chatModel = 'grok-2',
}: {
  browser: Browser;
  name: string;
  chatModel?: 'grok-2' | 'grok-3';
}): Promise<UserContext> {
  const directory = path.join(__dirname, '../playwright/.sessions');

  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }

  const storageFile = path.join(directory, `${name}.json`);

  const context = await browser.newContext();
  const page = await context.newPage();

  const email = `test-${name}@playwright.com`;
  const password = generateId(16);

  // TODO: Update this to use invite tokens or create users directly in the database
  // For now, we'll create users directly through the database instead of registration
  // This is a temporary workaround until proper invite token generation is implemented for tests

  // Skip the registration process for now and create user directly
  // This would need to be implemented with proper database access in tests
  await page.goto('http://localhost:3000/login');
  await page.getByPlaceholder('<EMAIL>').click();
  await page.getByPlaceholder('<EMAIL>').fill(email);
  await page.getByLabel('Password').click();
  await page.getByLabel('Password').fill(password);
  await page.getByRole('button', { name: 'Sign In' }).click();

  // This will fail for now since the user doesn't exist
  // Tests using this function will need to be updated

  const chatPage = new ChatPage(page);
  await chatPage.createNewChat();
  await chatPage.chooseModelFromSelector('grok-3');
  await expect(chatPage.getSelectedModel()).resolves.toEqual('Reasoning model');

  await page.waitForTimeout(1000);
  await context.storageState({ path: storageFile });
  await page.close();

  const newContext = await browser.newContext({ storageState: storageFile });
  const newPage = await newContext.newPage();

  return {
    context: newContext,
    page: newPage,
    request: newContext.request,
  };
}

export function generateRandomTestUser() {
  const email = `test-${getUnixTime(new Date())}@playwright.com`;
  const password = generateId(16);

  return {
    email,
    password,
  };
}
