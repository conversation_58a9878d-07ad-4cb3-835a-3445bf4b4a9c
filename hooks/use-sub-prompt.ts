'use client';

import useS<PERSON> from 'swr';
import type { SubPrompt } from '@/lib/db/schema';

const fetcher = async (url: string): Promise<SubPrompt> => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`);
  }
  return response.json();
};

/**
 * Hook to fetch a sub-prompt by ID with automatic caching and deduplication.
 * Uses SWR to ensure that multiple components requesting the same sub-prompt
 * will share the same request and cached result.
 */
export function useSubPrompt(subPromptId: string | null | undefined) {
  const { data, error, isLoading } = useSWR(
    subPromptId ? `/api/sub-prompts/${subPromptId}` : null,
    fetcher,
    {
      // Cache for 5 minutes since sub-prompts don't change frequently
      dedupingInterval: 5 * 60 * 1000,
      // Keep data fresh for 1 minute
      focusThrottleInterval: 60 * 1000,
      // Don't revalidate on window focus since sub-prompts are relatively static
      revalidateOnFocus: false,
    }
  );

  return {
    subPrompt: data,
    isLoading,
    error,
    subPromptName: data?.name,
  };
}

const subPromptsFetcher = async (url: string): Promise<SubPrompt[]> => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}`);
  }
  return response.json();
};

/**
 * Hook to fetch sub-prompts by parent ID with automatic caching and deduplication.
 * Uses SWR to ensure that multiple components requesting sub-prompts for the same parent
 * will share the same request and cached result.
 */
export function useSubPromptsByParentId(parentId: string | null | undefined) {
  const { data, error, isLoading, mutate } = useSWR(
    parentId ? `/api/sub-prompts?parentId=${parentId}` : null,
    subPromptsFetcher,
    {
      // Cache for 5 minutes since sub-prompts don't change frequently
      dedupingInterval: 5 * 60 * 1000,
      // Keep data fresh for 1 minute
      focusThrottleInterval: 60 * 1000,
      // Don't revalidate on window focus since sub-prompts are relatively static
      revalidateOnFocus: false,
    }
  );

  return {
    subPrompts: data || [],
    isLoading,
    error,
    mutate, // Expose mutate for manual revalidation after updates
  };
}
