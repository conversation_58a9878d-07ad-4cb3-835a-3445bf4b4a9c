@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer base {
  :root {
    /* Warm, cozy therapy-themed light mode */
    --background: 40 30% 98%;
    --foreground: 30 15% 15%;
    --card: 35 25% 97%;
    --card-foreground: 30 15% 15%;
    --popover: 35 25% 97%;
    --popover-foreground: 30 15% 15%;
    --primary: 25 25% 60%; /* Muted light brown */
    --primary-foreground: 40 30% 98%;
    --secondary: 30 20% 90%; /* Light sandy color */
    --secondary-foreground: 25 20% 35%;
    --muted: 30 20% 94%;
    --muted-foreground: 25 10% 45%;
    --accent: 25 30% 65%; /* More muted light brown accent */
    --accent-foreground: 30 15% 20%;
    --destructive: 0 65% 60%;
    --destructive-foreground: 40 30% 98%;
    --border: 25 15% 85%;
    --input: 25 15% 85%;
    --ring: 25 25% 60%;
    --chart-1: 25 65% 60%;
    --chart-2: 40 60% 70%;
    --chart-3: 15 50% 55%;
    --chart-4: 35 70% 65%;
    --chart-5: 20 65% 50%;
    --radius: 0.75rem; /* Slightly larger radius for softer appearance */
    --sidebar-background: 30 25% 95%;
    --sidebar-foreground: 25 15% 25%;
    --sidebar-primary: 25 25% 60%;
    --sidebar-primary-foreground: 40 30% 98%;
    --sidebar-accent: 25 25% 75%; /* Much lighter, more muted accent for sidebar */
    --sidebar-accent-foreground: 30 15% 20%;
    --sidebar-border: 25 15% 85%;
    --sidebar-ring: 25 25% 60%;
  }
  .dark {
    /* Warm, cozy therapy-themed dark mode */
    --background: 25 15% 12%;
    --foreground: 30 10% 92%;
    --card: 25 15% 15%;
    --card-foreground: 30 10% 92%;
    --popover: 25 15% 15%;
    --popover-foreground: 30 10% 92%;
    --primary: 25 30% 55%; /* Warm brown for dark mode */
    --primary-foreground: 30 15% 95%;
    --secondary: 25 15% 25%;
    --secondary-foreground: 30 10% 92%;
    --muted: 25 15% 20%;
    --muted-foreground: 30 10% 70%;
    --accent: 25 25% 50%; /* More muted brown accent for dark mode */
    --accent-foreground: 30 10% 92%;
    --destructive: 0 65% 45%;
    --destructive-foreground: 30 10% 92%;
    --border: 25 15% 25%;
    --input: 25 15% 25%;
    --ring: 25 30% 55%;
    --chart-1: 25 55% 55%;
    --chart-2: 40 50% 60%;
    --chart-3: 15 45% 45%;
    --chart-4: 35 55% 50%;
    --chart-5: 20 50% 40%;
    --sidebar-background: 25 15% 10%;
    --sidebar-foreground: 30 10% 90%;
    --sidebar-primary: 25 30% 55%;
    --sidebar-primary-foreground: 30 15% 95%;
    --sidebar-accent: 25 20% 40%; /* More muted accent for dark mode sidebar */
    --sidebar-accent-foreground: 30 15% 92%;
    --sidebar-border: 25 15% 25%;
    --sidebar-ring: 25 30% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  .overscroll-contain {
    overscroll-behavior: contain;
  }
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^="text-"] {
    color: transparent;
    @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background dark:bg-zinc-800 outline-none selection:bg-zinc-900 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-zinc-200 dark:bg-zinc-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent !important;
}

.cm-activeLine {
  @apply rounded-r-sm !important;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm !important;
}

.suggestion-highlight {
  @apply bg-primary/15 hover:bg-primary/25 dark:hover:bg-primary/30 dark:text-primary-foreground dark:bg-primary/20;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/20 rounded-full hover:bg-primary/30 transition-colors;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/30;
}

/* Add soft transitions for elements */
button,
a,
input,
textarea,
.card,
.message,
.bubble {
  @apply transition-all duration-200;
}

html,
body {
  overflow: hidden;
  overscroll-behavior: none;
}
