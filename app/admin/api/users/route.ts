import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getAllUsers } from '@/lib/db/queries';

export async function GET() {
  const session = await auth();

  if (!session?.user || session.user.type !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const users = await getAllUsers();
    return NextResponse.json(users);
  } catch (error) {
    console.error('Failed to fetch users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
} 