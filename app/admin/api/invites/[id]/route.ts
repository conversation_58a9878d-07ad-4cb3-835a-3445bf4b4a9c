import { NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { getInviteById } from '@/lib/db/queries';
import { db } from '@/lib/db';
import { invite } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user || session.user.type !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const inviteId = params.id;

    // Check if the invite exists and belongs to the current admin
    const existingInvite = await getInviteById(inviteId);
    if (!existingInvite) {
      return NextResponse.json(
        { error: 'Invite not found' },
        { status: 404 }
      );
    }

    if (existingInvite.createdBy !== session.user.id) {
      return NextResponse.json(
        { error: 'You can only revoke invites you created' },
        { status: 403 }
      );
    }

    // Check if the invite has already been used
    if (existingInvite.usedAt) {
      return NextResponse.json(
        { error: 'Cannot revoke an invite that has already been used' },
        { status: 400 }
      );
    }

    // Delete the invite
    await db
      .delete(invite)
      .where(
        and(
          eq(invite.id, inviteId),
          eq(invite.createdBy, session.user.id)
        )
      );

    return NextResponse.json({
      success: true,
      message: 'Invite revoked successfully',
    });
  } catch (error) {
    console.error('Failed to revoke invite:', error);
    return NextResponse.json(
      { error: 'Failed to revoke invite' },
      { status: 500 }
    );
  }
}
