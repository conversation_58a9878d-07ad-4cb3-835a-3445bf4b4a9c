import { NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { createInvite, getInvitesByCreator } from '@/lib/db/queries';
import { generateInviteToken } from '@/lib/auth/tokens';

const createInviteSchema = z.object({
  email: z.string().email(),
});

export async function GET(request: Request) {
  const session = await auth();

  if (!session?.user || session.user.type !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const invites = await getInvitesByCreator(session.user.id);

    return NextResponse.json({
      success: true,
      invites,
    });
  } catch (error) {
    console.error('Failed to fetch invites:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invites' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user || session.user.type !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const validatedData = createInviteSchema.parse(body);

    // Generate random token
    const token = await generateInviteToken();

    // Create invite record
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
    const invite = await createInvite({
      email: validatedData.email,
      token,
      expiresAt,
      createdBy: session.user.id,
    });

    // Generate the invite URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const inviteUrl = `${baseUrl}/register?invite=${encodeURIComponent(token)}`;

    return NextResponse.json({
      success: true,
      invite: {
        id: invite.id,
        email: validatedData.email,
        token,
        inviteUrl,
        expiresAt,
      },
    });
  } catch (error) {
    console.error('Failed to create invite:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create invite' },
      { status: 500 }
    );
  }
}
