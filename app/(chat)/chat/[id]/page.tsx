import { cookies } from 'next/headers';
import { notFound, redirect } from 'next/navigation';

import { auth } from '@/app/(auth)/auth';
import { Chat } from '@/components/chat';
import { getChatById, getMessagesByChatId } from '@/lib/db/queries';
import { DataStreamHandler } from '@/components/data-stream-handler';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { convertDbMessagesToUiMessages } from '@/lib/utils';

// Add a delay parameter for development testing if needed
// You can remove this in production
const ARTIFICIAL_DELAY = process.env.NODE_ENV === 'development' ? 0 : 0;

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;

  // Artificial delay for testing loading states (remove in production)
  if (ARTIFICIAL_DELAY > 0) {
    await new Promise((resolve) => setTimeout(resolve, ARTIFICIAL_DELAY));
  }

  const chat = await getChatById({ id });

  if (!chat) {
    notFound();
  }

  const session = await auth();

  if (!session) {
    redirect('/api/auth/guest');
  }

  if (chat.visibility === 'private') {
    if (!session.user) {
      return notFound();
    }

    if (session.user.id !== chat.userId) {
      return notFound();
    }
  }

  const messagesFromDb = await getMessagesByChatId({
    id,
  });

  const cookieStore = await cookies();
  const chatModelFromCookie = cookieStore.get('selected-model');
  const promptIdFromCookie = cookieStore.get('selected-prompt');
  const hasSeenFirstMessageModal = cookieStore.get('first-message-modal-shown')?.value === 'true';

  if (!chatModelFromCookie) {
    return (
      <>
        <Chat
          id={chat.id}
          initialMessages={convertDbMessagesToUiMessages(messagesFromDb)}
          initialChatModel={DEFAULT_CHAT_MODEL}
          initialVisibilityType={chat.visibility}
          isReadonly={session?.user?.id !== chat.userId}
          session={session}
          autoResume={true}
          initialPromptId={promptIdFromCookie?.value}
          hasSeenFirstMessageModal={hasSeenFirstMessageModal}
        />
        <DataStreamHandler id={id} />
      </>
    );
  }

  return (
    <>
      <Chat
        id={chat.id}
        initialMessages={convertDbMessagesToUiMessages(messagesFromDb)}
        initialChatModel={chatModelFromCookie.value}
        initialVisibilityType={chat.visibility}
        isReadonly={session?.user?.id !== chat.userId}
        session={session}
        autoResume={true}
        initialPromptId={promptIdFromCookie?.value}
        hasSeenFirstMessageModal={hasSeenFirstMessageModal}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
