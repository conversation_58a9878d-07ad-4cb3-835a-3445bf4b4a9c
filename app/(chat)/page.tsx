import { cookies } from 'next/headers';

import { Chat } from '@/components/chat';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { generateUUID } from '@/lib/utils';
import { DataStreamHandler } from '@/components/data-stream-handler';
import { auth } from '../(auth)/auth';
import { redirect } from 'next/navigation';

export default async function Page() {
  const session = await auth();

  if (!session) {
    redirect('/login');
  }

  const id = generateUUID();

  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get('selected-model');
  const promptIdFromCookie = cookieStore.get('selected-prompt');
  const hasSeenFirstMessageModal = cookieStore.get('first-message-modal-shown')?.value === 'true';

  if (!modelIdFromCookie) {
    return (
      <>
        <Chat
          key={id}
          id={id}
          initialMessages={[]}
          initialChatModel={DEFAULT_CHAT_MODEL}
          initialVisibilityType="public"
          isReadonly={false}
          session={session}
          autoResume={false}
          initialPromptId={promptIdFromCookie?.value}
          hasSeenFirstMessageModal={hasSeenFirstMessageModal}
        />
        <DataStreamHandler id={id} />
      </>
    );
  }

  return (
    <>
      <Chat
        key={id}
        id={id}
        initialMessages={[]}
        initialChatModel={modelIdFromCookie.value}
        initialVisibilityType="public"
        isReadonly={false}
        session={session}
        autoResume={false}
        initialPromptId={promptIdFromCookie?.value}
        hasSeenFirstMessageModal={hasSeenFirstMessageModal}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
