import { NextResponse } from 'next/server';
import { z } from 'zod';
import { getInviteByToken } from '@/lib/db/queries';

const validateInviteSchema = z.object({
  token: z.string().min(1),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = validateInviteSchema.parse(body);

    // Look up the invite by token
    const invite = await getInviteByToken(validatedData.token);
    if (!invite) {
      return NextResponse.json(
        { error: 'Invalid invite token' },
        { status: 400 }
      );
    }

    // Check if the invite has already been used
    if (invite.usedAt) {
      return NextResponse.json(
        { error: 'Invite has already been used' },
        { status: 400 }
      );
    }

    // Check if the invite has expired
    if (new Date() > invite.expiresAt) {
      return NextResponse.json(
        { error: 'Invite has expired' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      valid: true,
      email: invite.email,
      inviteId: invite.id,
    });
  } catch (error) {
    console.error('Failed to validate invite:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to validate invite' },
      { status: 500 }
    );
  }
}
