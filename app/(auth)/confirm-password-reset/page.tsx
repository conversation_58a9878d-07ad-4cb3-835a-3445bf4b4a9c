'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useActionState, useEffect, useState, Suspense } from 'react';
import { toast } from '@/components/toast';
import { useTranslations } from 'next-intl';

import { NewPasswordForm } from '@/components/new-password-form';
import { SubmitButton } from '@/components/submit-button';

import {
  resetPassword,
  type ResetPasswordConfirmActionState,
} from '../actions';

// Create a separate client component that uses searchParams
function PasswordResetForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const t = useTranslations('auth');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isExpired, setIsExpired] = useState(false);

  const [state, formAction] = useActionState<
    ResetPasswordConfirmActionState,
    FormData
  >(resetPassword, {
    status: 'idle',
  });

  useEffect(() => {
    if (!token) {
      toast({
        type: 'error',
        description: t('invalidResetToken'),
      });
      router.push('/reset-password');
      return;
    }
  }, [token, router, t]);

  useEffect(() => {
    if (state.status === 'failed') {
      toast({
        type: 'error',
        description: t('resetPasswordFailed'),
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: t('passwordRequirements'),
      });
    } else if (state.status === 'token_expired') {
      setIsExpired(true);
      toast({
        type: 'error',
        description: t('resetLinkExpired'),
      });
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      toast({
        type: 'success',
        description: t('passwordResetSuccess'),
      });
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    }
  }, [state.status, router, t]);

  const handleSubmit = (formData: FormData) => {
    const password = formData.get('password') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    if (password !== confirmPassword) {
      toast({
        type: 'error',
        description: t('passwordsDoNotMatch'),
      });
      return;
    }

    formAction(formData);
  };

  if (isExpired) {
    return (
      <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
        <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
          <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
            <h3 className="text-xl font-semibold dark:text-zinc-50">
              {t('linkExpired')}
            </h3>
            <p className="text-sm text-gray-500 dark:text-zinc-400">
              {t('linkExpiredDescription')}
            </p>
            <div className="mt-4">
              <Link
                href="/reset-password"
                className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
              >
                {t('requestNewResetLink')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!token) {
    return null; // Router will redirect, no need to show anything
  }

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">
            {t('setNewPassword')}
          </h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            {t('setNewPasswordDescription')}
          </p>
        </div>
        <NewPasswordForm action={handleSubmit} token={token}>
          <SubmitButton isSuccessful={isSuccessful}>
            {t('resetPassword')}
          </SubmitButton>
          <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
            <Link
              href="/login"
              className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
            >
              {t('backToLogin')}
            </Link>
          </p>
        </NewPasswordForm>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function Page() {
  const t = useTranslations('common');
  
  return (
    <Suspense
      fallback={
        <div className="flex h-dvh w-screen items-center justify-center bg-background">
          <div className="w-full max-w-md overflow-hidden rounded-2xl">
            <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
              <h3 className="text-xl font-semibold dark:text-zinc-50">
                {t('loading')}
              </h3>
            </div>
          </div>
        </div>
      }
    >
      <PasswordResetForm />
    </Suspense>
  );
}
