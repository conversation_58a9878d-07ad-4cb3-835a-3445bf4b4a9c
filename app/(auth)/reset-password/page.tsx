'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useActionState, useEffect, useState } from 'react';
import { toast } from '@/components/toast';
import { useTranslations } from 'next-intl';

import { ResetPasswordForm } from '@/components/reset-password-form';
import { SubmitButton } from '@/components/submit-button';

import {
  requestPasswordReset,
  type ResetPasswordActionState,
} from '../actions';

export default function Page() {
  const router = useRouter();
  const t = useTranslations('auth');
  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);

  const [state, formAction] = useActionState<
    ResetPasswordActionState,
    FormData
  >(requestPasswordReset, {
    status: 'idle',
  });

  useEffect(() => {
    if (state.status === 'failed') {
      toast({
        type: 'error',
        description: t('resetEmailFailed'),
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: t('validEmailRequired'),
      });
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      toast({
        type: 'success',
        description: t('resetEmailSent'),
      });

      // Redirect to login page after a short delay
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    }
  }, [state.status, router, t]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">
            {t('resetPassword')}
          </h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            {t('resetPasswordDescription')}
          </p>
        </div>
        <ResetPasswordForm action={handleSubmit} defaultEmail={email}>
          <SubmitButton isSuccessful={isSuccessful}>
            {t('sendResetLink')}
          </SubmitButton>
          <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
            <Link
              href="/login"
              className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
            >
              {t('backToLogin')}
            </Link>
          </p>
        </ResetPasswordForm>
      </div>
    </div>
  );
}
