'use server';

export type EmailContent = {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
};

/**
 * Sends an email using MailerSend API
 */
export async function sendEmail({ to, subject, htmlContent, textContent }: EmailContent): Promise<boolean> {
  const apiKey = process.env.MAILERSEND_API_KEY;
  const senderDomain = process.env.MAILERSEND_SENDER_DOMAIN;

  if (process.env.NODE_ENV === 'development') {
    console.log('Sending email to', to);
    console.log('Subject:', subject);
    console.log('Content:', textContent || htmlContent);
    return true;
  }

  if (!apiKey || !senderDomain) {
    console.error('Missing required environment variables for email sending');
    return false;
  }

  try {
    const response = await fetch('https://api.mailersend.com/v1/email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${api<PERSON>ey}`,
      },
      body: JSON.stringify({
        from: {
          email: `no-reply@${senderDomain}`,
          name: '<PERSON><PERSON> App',
        },
        to: [
          {
            email: to,
          },
        ],
        subject,
        html: htmlContent,
        text: textContent,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Failed to send email:', errorData);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}

/**
 * Sends a password reset email with the reset link
 */
export async function sendPasswordResetEmail(email: string, resetToken: string): Promise<boolean> {
  // Create the reset URL with the token
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const resetUrl = `${baseUrl}/confirm-password-reset?token=${encodeURIComponent(resetToken)}`;

  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Reset Your Password</h2>
      <p>You recently requested to reset your password for your Enai account. 
         Click the button below to reset it. This link is valid for 30 minutes.</p>
      <a href="${resetUrl}" style="display: inline-block; background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 16px 0;">Reset Password</a>
      <p>If you did not request a password reset, please ignore this email or contact support if you have questions.</p>
      <p>If the button above doesn't work, copy and paste the following link into your browser:</p>
      <p>${resetUrl}</p>
    </div>
  `;

  const textContent = `
    Reset Your Password
    
    You recently requested to reset your password for your Enai account.
    Click the link below to reset it. This link is valid for 30 minutes.
    
    ${resetUrl}
    
    If you did not request a password reset, please ignore this email or contact support if you have questions.
  `;

  return sendEmail({
    to: email,
    subject: 'Reset Your Password - Enai',
    htmlContent,
    textContent,
  });
}