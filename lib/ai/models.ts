export const DEFAULT_CHAT_MODEL: string = 'grok-2';

export interface ChatModel {
  id: string;
  name: string;
  description: string;
}

export const chatModels: Array<ChatModel> = [
  {
    id: 'grok-2',
    name: 'Grok 2',
    description: 'Old Grok model from xAI',
  },
  {
    id: 'grok-3',
    name: 'Grok 3',
    description: 'Newest Grok model with reasoning',
  },
  {
    id: 'openai-gpt-4o',
    name: 'GPT 4o',
    description: "OpenAI's flagship model",
  },
  // {
  //   id: 'openai-gpt-4o-mini',
  //   name: 'GPT 4o Mini',
  //   description: 'Smaller and faster than GPT 4o',
  // },
  // {
  //   id: 'openai-gpt-4.1-mini',
  //   name: 'GPT 4.1 Mini',
  //   description: 'New flagship model from OpenAI',
  // },
  // {
  //   id: 'openai-o4-mini',
  //   name: 'OpenAI o4 Mini',
  //   description: 'Newest reasoning model from OpenAI',
  // },
  // {
  //   id: 'claude-3-5-sonnet',
  //   name: 'Claude 3.5 Sonnet',
  //   description: 'Mid-sized model from Anthropic',
  // },
  // {
  //   id: 'claude-3-5-haiku',
  //   name: 'Claude 3.5 Haiku',
  //   description: 'Smallest model from Anthropic',
  // },
  // {
  //   id: 'claude-3-7-sonnet',
  //   name: 'Claude 3.7 Sonnet',
  //   description: 'Previous reasoning model from Anthropic',
  // },
  {
    id: 'claude-sonnet-4',
    name: 'Claude 4 Sonnet',
    description: 'Latest reasoning model from Anthropic',
  },
  // {
  //   id: 'cerebras-llama-4-scout',
  //   name: 'Llama 4 Scout',
  //   description: 'Mid-sized Llama model hosted by Cerebras',
  // },
  // {
  //   id: 'mistral-small',
  //   name: 'Mistral Small',
  //   description: 'Small-sized Mistral model',
  // },
  // {
  //   id: 'mistral-medium',
  //   name: 'Mistral Medium',
  //   description: 'Medium-sized Mistral model',
  // },
  // {
  //   id: 'mistral-nemo',
  //   name: 'Mistral Nemo',
  //   description: 'Older research Mistral model',
  // },
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini 2.5 Flash',
    description: 'Newest Gemini model from Google',
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: 'Most capable reasoning Gemini model',
  },
];

export const chatModelIds = chatModels.map((model) => model.id);
