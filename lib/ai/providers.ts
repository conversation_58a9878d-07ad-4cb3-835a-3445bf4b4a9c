import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { xai } from '@ai-sdk/xai';
import { openai } from '@ai-sdk/openai';
import { anthropic } from '@ai-sdk/anthropic';
import { isTestEnvironment } from '../constants';
import { chatModel } from './models.test';
import { google } from '@ai-sdk/google';
export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'grok-2': chatModel,
      },
    })
  : customProvider({
      languageModels: {
        'grok-2': xai('grok-2-1212'),
        'grok-3': wrapLanguageModel({
          model: xai('grok-3-mini'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'openai-gpt-4o': openai('gpt-4o'),
        // 'openai-gpt-4o-mini': openai('gpt-4o-mini'),
        // 'openai-gpt-4.1-mini': openai('gpt-4.1-mini'),
        // 'openai-o4-mini': openai('o4-mini'),
        // 'claude-3-5-sonnet': anthropic('claude-3-5-sonnet-20241022'),
        // 'claude-3-5-haiku': anthropic('claude-3-5-haiku-20241022'),
        // 'claude-3-7-sonnet': anthropic('claude-3-7-sonnet-20250219', {
        //   sendReasoning: true,
        // }),
        'claude-sonnet-4': anthropic('claude-sonnet-4-20250514', {
          sendReasoning: true,
        }),
        // 'mistral-small': mistral('mistral-small-latest'),
        // 'mistral-medium': mistral('mistral-medium-latest'),
        // 'mistral-nemo': mistral('open-mistral-nemo'),
        // 'cerebras-llama-4-scout': cerebras('llama-4-scout-17b-16e-instruct'),
        'gemini-2.5-flash': google('gemini-2.5-flash'),
        'gemini-2.5-pro': google('gemini-2.5-pro'),
      },
    });
