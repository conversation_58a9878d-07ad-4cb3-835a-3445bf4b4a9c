'use server';

import { SignJWT, jwtVerify } from 'jose';
import { z } from 'zod';
import { randomBytes } from 'crypto';

// Schema for password reset token payload
const resetTokenPayloadSchema = z.object({
  email: z.string().email(),
  timestamp: z.number(),
});

type ResetTokenPayload = z.infer<typeof resetTokenPayloadSchema>;

/**
 * Generates a random 12-character token with lowercase letters and numbers
 * @returns Random token string
 */
export async function generateInviteToken(): Promise<string> {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Generates a JWT token for password reset
 * @param email User's email address
 * @returns JWT token string
 */
export async function generatePasswordResetToken(email: string): Promise<string> {
  if (!process.env.AUTH_SECRET) {
    throw new Error('AUTH_SECRET is not defined');
  }

  const tokenPayload: ResetTokenPayload = {
    email,
    // Current timestamp in seconds
    timestamp: Math.floor(Date.now() / 1000),
  };

  const secret = new TextEncoder().encode(process.env.AUTH_SECRET);
  
  // Create the JWT token
  const token = await new SignJWT(tokenPayload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('30m') // Token expires in 30 minutes
    .sign(secret);

  return token;
}

/**
 * Verifies a password reset token
 * @param token JWT token to verify
 * @returns The email address if token is valid, null if expired or invalid
 */
export async function verifyPasswordResetToken(token: string): Promise<string | null> {
  if (!process.env.AUTH_SECRET) {
    throw new Error('AUTH_SECRET is not defined');
  }

  const secret = new TextEncoder().encode(process.env.AUTH_SECRET);

  try {
    const { payload } = await jwtVerify(token, secret, {
      maxTokenAge: '30m', // Confirm token is not older than 30 minutes
    });

    // Validate the payload structure
    const validatedPayload = resetTokenPayloadSchema.parse(payload);
    
    // Check if token is expired (30 minutes = 1800 seconds)
    const currentTime = Math.floor(Date.now() / 1000);
    if (currentTime - validatedPayload.timestamp > 1800) {
      return null; // Token expired
    }

    return validatedPayload.email;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null; // Invalid token
  }
}



