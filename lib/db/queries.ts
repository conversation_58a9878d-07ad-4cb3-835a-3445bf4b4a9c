import 'server-only';

import {
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  gte,
  inArray,
  lt,
  type SQL,
} from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

import {
  user,
  chat,
  type User,
  document,
  type Suggestion,
  suggestion,
  message,
  vote,
  type DBMessage,
  type Chat,
  stream,
  prompt,
  subPrompt,
  setting,
  comment,
  invite,
  type Invite,
} from './schema';
import type { ArtifactKind } from '@/components/artifact';
import { generateUUID } from '../utils';
import { generateHashedPassword } from './utils';
import type { VisibilityType } from '@/components/visibility-selector';

// Optionally, if not using email/pass login, you can
// use the Drizzle adapter for Auth.js / NextAuth
// https://authjs.dev/reference/adapter/drizzle

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function getUser(email: string): Promise<Array<User>> {
  try {
    return await db.select().from(user).where(eq(user.email, email));
  } catch (error) {
    console.error('Failed to get user from database');
    throw error;
  }
}

export async function updateChatTitleById({
  chatId,
  title,
}: {
  chatId: string;
  title: string;
}) {
  try {
    const [updatedChat] = await db
      .update(chat)
      .set({ title })
      .where(eq(chat.id, chatId))
      .returning();
    return updatedChat;
  } catch (error) {
    console.error('Failed to update chat title in database');
    throw error;
  }
}

export async function getUserById(id: string): Promise<User | undefined> {
  try {
    const [foundUser] = await db.select().from(user).where(eq(user.id, id));
    return foundUser;
  } catch (error) {
    console.error('Failed to get user by ID from database');
    throw error;
  }
}

export async function createUser(
  email: string,
  password: string,
  type: User['type'] = 'user',
) {
  const hashedPassword = generateHashedPassword(password);

  try {
    return await db
      .insert(user)
      .values({ email, password: hashedPassword, type });
  } catch (error) {
    console.error('Failed to create user in database');
    throw error;
  }
}

export async function getAllUsers(): Promise<User[]> {
  try {
    return await db.select().from(user).orderBy(asc(user.email));
  } catch (error) {
    console.error('Failed to get all users from database');
    throw error;
  }
}

export async function createGuestUser() {
  const email = `guest-${Date.now()}`;
  const password = generateHashedPassword(generateUUID());

  try {
    return await db
      .insert(user)
      .values({ email, password, type: 'user' })
      .returning({
        id: user.id,
        email: user.email,
        type: user.type,
      });
  } catch (error) {
    console.error('Failed to create guest user in database');
    throw error;
  }
}

export async function saveChat({
  id,
  userId,
  title,
  visibility,
}: {
  id: string;
  userId: string;
  title: string;
  visibility: VisibilityType;
}) {
  try {
    return await db.insert(chat).values({
      id,
      createdAt: new Date(),
      userId,
      title,
      visibility,
    });
  } catch (error) {
    console.error('Failed to save chat in database');
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    await db.delete(vote).where(eq(vote.chatId, id));
    await db.delete(message).where(eq(message.chatId, id));
    await db.delete(stream).where(eq(stream.chatId, id));

    const [chatsDeleted] = await db
      .delete(chat)
      .where(eq(chat.id, id))
      .returning();
    return chatsDeleted;
  } catch (error) {
    console.error('Failed to delete chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    const extendedLimit = limit + 1;

    const query = (whereCondition?: SQL<any>) =>
      db
        .select()
        .from(chat)
        .where(
          whereCondition
            ? and(whereCondition, eq(chat.userId, id))
            : eq(chat.userId, id),
        )
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);

    let filteredChats: Array<Chat> = [];

    if (startingAfter) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, startingAfter))
        .limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${startingAfter} not found`);
      }

      filteredChats = await query(gt(chat.createdAt, selectedChat.createdAt));
    } else if (endingBefore) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, endingBefore))
        .limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${endingBefore} not found`);
      }

      filteredChats = await query(lt(chat.createdAt, selectedChat.createdAt));
    } else {
      filteredChats = await query();
    }

    const hasMore = filteredChats.length > limit;

    return {
      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,
      hasMore,
    };
  } catch (error) {
    console.error('Failed to get chats by user from database');
    throw error;
  }
}

export async function getChatsByUserIdWithFirstMessage({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    const extendedLimit = limit + 1;

    // First, get the paginated chats
    const buildChatQuery = (whereCondition?: SQL<any>) => {
      const conditions = whereCondition
        ? and(whereCondition, eq(chat.userId, id))
        : eq(chat.userId, id);

      return db
        .select()
        .from(chat)
        .where(conditions)
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);
    };

    let chats: any[] = [];

    if (startingAfter) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, startingAfter))
        .limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${startingAfter} not found`);
      }

      chats = await buildChatQuery(gt(chat.createdAt, selectedChat.createdAt));
    } else if (endingBefore) {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(eq(chat.id, endingBefore))
        .limit(1);

      if (!selectedChat) {
        throw new Error(`Chat with id ${endingBefore} not found`);
      }

      chats = await buildChatQuery(lt(chat.createdAt, selectedChat.createdAt));
    } else {
      chats = await buildChatQuery();
    }

    if (chats.length === 0) {
      return { chats: [], hasMore: false };
    }

    // Get the chat IDs
    const chatIds = chats.map((chat) => chat.id);

    // Get the first user message for each chat
    const messagesWithPrompts = await db
      .select({
        chatId: message.chatId,
        messageId: message.id,
        messageRole: message.role,
        messageModelId: message.modelId,
        messagePromptId: message.promptId,
        messageParts: message.parts,
        messageCreatedAt: message.createdAt,
        promptName: prompt.name,
      })
      .from(message)
      .leftJoin(prompt, eq(prompt.id, message.promptId))
      .where(and(inArray(message.chatId, chatIds), eq(message.role, 'user')))
      .orderBy(asc(message.createdAt));

    // Create a map of chat ID to first user message
    const firstMessageMap = new Map();
    for (const msg of messagesWithPrompts) {
      if (!firstMessageMap.has(msg.chatId)) {
        firstMessageMap.set(msg.chatId, {
          id: msg.messageId,
          role: msg.messageRole,
          modelId: msg.messageModelId,
          promptId: msg.messagePromptId,
          parts: msg.messageParts,
          createdAt: msg.messageCreatedAt,
          promptName: msg.promptName,
        });
      }
    }

    // Combine chats with their first messages
    const chatsWithFirstMessage = chats.map((chat) => ({
      id: chat.id,
      createdAt: chat.createdAt,
      title: chat.title,
      userId: chat.userId,
      visibility: chat.visibility,
      firstMessage: firstMessageMap.get(chat.id) || null,
    }));

    const hasMore = chatsWithFirstMessage.length > limit;

    return {
      chats: hasMore
        ? chatsWithFirstMessage.slice(0, limit)
        : chatsWithFirstMessage,
      hasMore,
    };
  } catch (error) {
    console.error(
      'Failed to get chats with first message by user from database',
    );
    throw error;
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  } catch (error) {
    console.error('Failed to get chat by id from database');
    throw error;
  }
}

export async function saveMessages({
  messages,
}: {
  messages: Array<DBMessage>;
}) {
  try {
    return await db.insert(message).values(messages);
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    return await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(asc(message.createdAt));
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    throw error;
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    throw error;
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    return await db
      .insert(document)
      .values({
        id,
        title,
        kind,
        content,
        userId,
        createdAt: new Date(),
      })
      .returning();
  } catch (error) {
    console.error('Failed to save document in database');
    throw error;
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    const documents = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(asc(document.createdAt));

    return documents;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    const [selectedDocument] = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(desc(document.createdAt));

    return selectedDocument;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    await db
      .delete(suggestion)
      .where(
        and(
          eq(suggestion.documentId, id),
          gt(suggestion.documentCreatedAt, timestamp),
        ),
      );

    return await db
      .delete(document)
      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)))
      .returning();
  } catch (error) {
    console.error(
      'Failed to delete documents by id after timestamp from database',
    );
    throw error;
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error('Failed to save suggestions in database');
    throw error;
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error(
      'Failed to get suggestions by document version from database',
    );
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    const messagesToDelete = await db
      .select({ id: message.id })
      .from(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );

    const messageIds = messagesToDelete.map((message) => message.id);

    if (messageIds.length > 0) {
      await db
        .delete(vote)
        .where(
          and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)),
        );

      return await db
        .delete(message)
        .where(
          and(eq(message.chatId, chatId), inArray(message.id, messageIds)),
        );
    }
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}

export async function getMessageCountByUserId({
  id,
  differenceInHours,
}: {
  id: string;
  differenceInHours: number;
}) {
  try {
    const twentyFourHoursAgo = new Date(
      Date.now() - differenceInHours * 60 * 60 * 1000,
    );

    const [stats] = await db
      .select({ count: count(message.id) })
      .from(message)
      .innerJoin(chat, eq(message.chatId, chat.id))
      .where(
        and(
          eq(chat.userId, id),
          gte(message.createdAt, twentyFourHoursAgo),
          eq(message.role, 'user'),
        ),
      )
      .execute();

    return stats?.count ?? 0;
  } catch (error) {
    console.error(
      'Failed to get message count by user id for the last 24 hours from database',
    );
    throw error;
  }
}

export async function createStreamId({
  streamId,
  chatId,
}: {
  streamId: string;
  chatId: string;
}) {
  try {
    await db
      .insert(stream)
      .values({ id: streamId, chatId, createdAt: new Date() });
  } catch (error) {
    console.error('Failed to create stream id in database');
    throw error;
  }
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  try {
    const streamIds = await db
      .select({ id: stream.id })
      .from(stream)
      .where(eq(stream.chatId, chatId))
      .orderBy(desc(stream.createdAt))
      .execute();

    return streamIds.map(({ id }) => id);
  } catch (error) {
    console.error('Failed to get stream ids by chat id from database');
    throw error;
  }
}

export async function deleteMessageById({
  chatId,
  messageId,
}: {
  chatId: string;
  messageId: string;
}) {
  try {
    // First delete any votes associated with this message
    await db
      .delete(vote)
      .where(and(eq(vote.chatId, chatId), eq(vote.messageId, messageId)));

    // Then delete the message itself
    return await db
      .delete(message)
      .where(and(eq(message.chatId, chatId), eq(message.id, messageId)))
      .returning();
  } catch (error) {
    console.error('Failed to delete message from database', error);
    throw error;
  }
}

export async function getPrompts() {
  return db
    .select()
    .from(prompt)
    .where(eq(prompt.isDeleted, false))
    .orderBy(desc(prompt.createdAt));
}

export async function getPromptById({ id }: { id: string }) {
  try {
    const [selectedPrompt] = await db
      .select()
      .from(prompt)
      .where(and(eq(prompt.id, id), eq(prompt.isDeleted, false)));
    return selectedPrompt;
  } catch (error) {
    console.error('Failed to get prompt by id from database');
    throw error;
  }
}

export async function createPrompt({
  name,
  content,
  variables = {},
}: {
  name: string;
  content: string;
  variables?: Record<string, string>;
}) {
  try {
    return await db.insert(prompt).values({
      name,
      content,
      variables: JSON.stringify(variables),
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Failed to create prompt in database');
    throw error;
  }
}

export async function updatePrompt({
  id,
  name,
  content,
  variables,
}: {
  id: string;
  name: string;
  content: string;
  variables?: Record<string, string>;
}) {
  try {
    const updateData: any = { name, content };
    if (variables !== undefined) {
      updateData.variables = JSON.stringify(variables);
    }

    return await db.update(prompt).set(updateData).where(eq(prompt.id, id));
  } catch (error) {
    console.error('Failed to update prompt in database');
    throw error;
  }
}

export async function deletePrompt({ id }: { id: string }) {
  try {
    // Soft delete by setting isDeleted to true
    return await db
      .update(prompt)
      .set({ isDeleted: true })
      .where(eq(prompt.id, id))
      .returning();
  } catch (error) {
    console.error('Failed to delete prompt from database');
    throw error;
  }
}

// Sub-prompt CRUD operations
export async function getSubPromptsByParentId({
  parentId,
}: { parentId: string }) {
  try {
    return await db
      .select()
      .from(subPrompt)
      .where(
        and(
          eq(subPrompt.parentPromptId, parentId),
          eq(subPrompt.isDeleted, false),
        ),
      )
      .orderBy(desc(subPrompt.createdAt));
  } catch (error) {
    console.error('Failed to get sub-prompts by parent id from database');
    throw error;
  }
}

export async function getSubPromptById({ id }: { id: string }) {
  try {
    // Fetch all non-deleted sub-prompts and manually match for partial ID matches
    const allSubPrompts = await db
      .select()
      .from(subPrompt)
      .where(eq(subPrompt.isDeleted, false));
    
    // Find the sub-prompt that matches the provided ID (allowing partial matches)
    const selectedSubPrompt = allSubPrompts.find(sp => sp.id.includes(id));
    
    return selectedSubPrompt;
  } catch (error) {
    console.error('Failed to get sub-prompt by id from database');
    throw error;
  }
}

export async function createSubPrompt({
  parentPromptId,
  name,
  content,
}: {
  parentPromptId: string;
  name: string;
  content: string;
}) {
  try {
    return await db
      .insert(subPrompt)
      .values({ parentPromptId, name, content, createdAt: new Date() })
      .returning();
  } catch (error) {
    console.error('Failed to create sub-prompt in database');
    throw error;
  }
}

export async function updateSubPrompt({
  id,
  name,
  content,
}: {
  id: string;
  name: string;
  content: string;
}) {
  try {
    return await db
      .update(subPrompt)
      .set({ name, content })
      .where(eq(subPrompt.id, id))
      .returning();
  } catch (error) {
    console.error('Failed to update sub-prompt in database');
    throw error;
  }
}

export async function deleteSubPrompt({ id }: { id: string }) {
  try {
    // Soft delete by setting isDeleted to true
    return await db
      .update(subPrompt)
      .set({ isDeleted: true })
      .where(eq(subPrompt.id, id))
      .returning();
  } catch (error) {
    console.error('Failed to delete sub-prompt from database');
    throw error;
  }
}

// Settings
export async function getSetting(key: string) {
  try {
    const row = await db.select().from(setting).where(eq(setting.key, key));
    return row;
  } catch (error) {
    console.error(`Failed to get setting with key ${key} from database`);
    throw error;
  }
}

export async function getAllSettings() {
  try {
    return await db.select().from(setting);
  } catch (error) {
    console.error('Failed to get all settings from database');
    throw error;
  }
}

export async function updateSetting(key: string, value: string | null) {
  try {
    return await db
      .update(setting)
      .set({ value, updatedAt: new Date() })
      .where(eq(setting.key, key))
      .returning();
  } catch (error) {
    console.error(`Failed to update setting with key ${key} in database`);
    throw error;
  }
}

export async function createSetting(key: string, value: string | null) {
  try {
    return await db
      .insert(setting)
      .values({
        key,
        value,
        updatedAt: new Date(),
      })
      .returning();
  } catch (error) {
    console.error(`Failed to create setting with key ${key} in database`);
    throw error;
  }
}

/**
 * Update a user's password
 * @param userId The ID of the user
 * @param hashedPassword The already hashed password to set
 */
export async function updateUserPassword(
  userId: string,
  hashedPassword: string,
) {
  try {
    return await db
      .update(user)
      .set({ password: hashedPassword })
      .where(eq(user.id, userId));
  } catch (error) {
    console.error('Failed to update user password in database');
    throw error;
  }
}

// Comment-related queries
export async function createComment({
  messageId,
  userId,
  content,
}: {
  messageId: string;
  userId: string;
  content: string;
}) {
  try {
    return await db
      .insert(comment)
      .values({
        messageId,
        userId,
        content,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();
  } catch (error) {
    console.error('Failed to create comment in database');
    throw error;
  }
}

export async function getCommentsByMessageId({
  messageId,
}: { messageId: string }) {
  try {
    return await db
      .select({
        id: comment.id,
        messageId: comment.messageId,
        userId: comment.userId,
        content: comment.content,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
        userEmail: user.email,
        userType: user.type,
      })
      .from(comment)
      .leftJoin(user, eq(comment.userId, user.id))
      .where(eq(comment.messageId, messageId))
      .orderBy(asc(comment.createdAt));
  } catch (error) {
    console.error('Failed to get comments by message id from database');
    throw error;
  }
}

export async function getCommentCountsByMessageIds({
  messageIds,
}: { messageIds: string[] }) {
  try {
    if (messageIds.length === 0) return [];

    return await db
      .select({
        messageId: comment.messageId,
        count: count(comment.id),
      })
      .from(comment)
      .where(inArray(comment.messageId, messageIds))
      .groupBy(comment.messageId);
  } catch (error) {
    console.error('Failed to get comment counts by message ids from database');
    throw error;
  }
}

export async function deleteComment({
  id,
  userId,
}: { id: string; userId: string }) {
  try {
    return await db
      .delete(comment)
      .where(and(eq(comment.id, id), eq(comment.userId, userId)))
      .returning();
  } catch (error) {
    console.error('Failed to delete comment from database');
    throw error;
  }
}

export async function updateComment({
  id,
  userId,
  content,
}: {
  id: string;
  userId: string;
  content: string;
}) {
  try {
    return await db
      .update(comment)
      .set({ content, updatedAt: new Date() })
      .where(and(eq(comment.id, id), eq(comment.userId, userId)))
      .returning();
  } catch (error) {
    console.error('Failed to update comment in database');
    throw error;
  }
}

// Invite-related queries
export async function createInvite({
  email,
  token,
  expiresAt,
  createdBy,
}: {
  email: string;
  token: string;
  expiresAt: Date;
  createdBy: string;
}): Promise<Invite> {
  try {
    const [newInvite] = await db
      .insert(invite)
      .values({
        email,
        token,
        expiresAt,
        createdBy,
        createdAt: new Date(),
      })
      .returning();
    return newInvite;
  } catch (error) {
    console.error('Failed to create invite in database');
    throw error;
  }
}

export async function getInviteByToken(token: string): Promise<Invite | undefined> {
  try {
    const [foundInvite] = await db
      .select()
      .from(invite)
      .where(eq(invite.token, token));
    return foundInvite;
  } catch (error) {
    console.error('Failed to get invite by token from database');
    throw error;
  }
}

export async function getInviteById(id: string): Promise<Invite | undefined> {
  try {
    const [foundInvite] = await db
      .select()
      .from(invite)
      .where(eq(invite.id, id));
    return foundInvite;
  } catch (error) {
    console.error('Failed to get invite by id from database');
    throw error;
  }
}

export async function markInviteAsUsed(id: string): Promise<void> {
  try {
    await db
      .update(invite)
      .set({ usedAt: new Date() })
      .where(eq(invite.id, id));
  } catch (error) {
    console.error('Failed to mark invite as used in database');
    throw error;
  }
}

export async function getInvitesByCreator(createdBy: string): Promise<Invite[]> {
  try {
    return await db
      .select()
      .from(invite)
      .where(eq(invite.createdBy, createdBy))
      .orderBy(desc(invite.createdAt));
  } catch (error) {
    console.error('Failed to get invites by creator from database');
    throw error;
  }
}


