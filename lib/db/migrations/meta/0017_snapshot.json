{"id": "5951846d-3fb1-4857-a820-797b3829377e", "prevId": "70a18631-fb1d-405b-b087-03e8c52b3851", "version": "7", "dialect": "postgresql", "tables": {"public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'public'"}}, "indexes": {}, "foreignKeys": {"Chat_userId_User_id_fk": {"name": "Chat_userId_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Comment": {"name": "Comment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Comment_messageId_Message_v2_id_fk": {"name": "Comment_messageId_Message_v2_id_fk", "tableFrom": "Comment", "tableTo": "Message_v2", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Comment_userId_User_id_fk": {"name": "Comment_userId_User_id_fk", "tableFrom": "Comment", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Document": {"name": "Document", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'text'"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Document_userId_User_id_fk": {"name": "Document_userId_User_id_fk", "tableFrom": "Document", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Document_id_createdAt_pk": {"name": "Document_id_createdAt_pk", "columns": ["id", "createdAt"]}}, "uniqueConstraints": {}}, "public.Invite": {"name": "Invite", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "usedAt": {"name": "usedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdBy": {"name": "created<PERSON>y", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Invite_createdBy_User_id_fk": {"name": "Invite_createdBy_User_id_fk", "tableFrom": "Invite", "tableTo": "User", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Message_v2": {"name": "Message_v2", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "modelId": {"name": "modelId", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "promptId": {"name": "promptId", "type": "uuid", "primaryKey": false, "notNull": false}, "subPromptId": {"name": "subPromptId", "type": "uuid", "primaryKey": false, "notNull": false}, "parts": {"name": "parts", "type": "json", "primaryKey": false, "notNull": true}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Message_v2_chatId_Chat_id_fk": {"name": "Message_v2_chatId_Chat_id_fk", "tableFrom": "Message_v2", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Message_v2_promptId_Prompt_id_fk": {"name": "Message_v2_promptId_Prompt_id_fk", "tableFrom": "Message_v2", "tableTo": "Prompt", "columnsFrom": ["promptId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Message_v2_subPromptId_SubPrompt_id_fk": {"name": "Message_v2_subPromptId_SubPrompt_id_fk", "tableFrom": "Message_v2", "tableTo": "SubPrompt", "columnsFrom": ["subPromptId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Message_chatId_Chat_id_fk": {"name": "Message_chatId_Chat_id_fk", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Prompt": {"name": "Prompt", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "variables": {"name": "variables", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "isDeleted": {"name": "isDeleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Setting": {"name": "Setting", "schema": "", "columns": {"key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Stream": {"name": "Stream", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Stream_chatId_Chat_id_fk": {"name": "Stream_chatId_Chat_id_fk", "tableFrom": "Stream", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Stream_id_pk": {"name": "Stream_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.SubPrompt": {"name": "SubPrompt", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parentPromptId": {"name": "parentPromptId", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "isDeleted": {"name": "isDeleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"SubPrompt_parentPromptId_Prompt_id_fk": {"name": "SubPrompt_parentPromptId_Prompt_id_fk", "tableFrom": "SubPrompt", "tableTo": "Prompt", "columnsFrom": ["parentPromptId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Suggestion": {"name": "Suggestion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": true, "default": "gen_random_uuid()"}, "documentId": {"name": "documentId", "type": "uuid", "primaryKey": false, "notNull": true}, "documentCreatedAt": {"name": "documentCreatedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "originalText": {"name": "originalText", "type": "text", "primaryKey": false, "notNull": true}, "suggestedText": {"name": "suggestedText", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isResolved": {"name": "isResolved", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Suggestion_userId_User_id_fk": {"name": "Suggestion_userId_User_id_fk", "tableFrom": "Suggestion", "tableTo": "User", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk": {"name": "Suggestion_documentId_documentCreatedAt_Document_id_createdAt_fk", "tableFrom": "Suggestion", "tableTo": "Document", "columnsFrom": ["documentId", "documentCreatedAt"], "columnsTo": ["id", "createdAt"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Suggestion_id_pk": {"name": "Suggestion_id_pk", "columns": ["id"]}}, "uniqueConstraints": {}}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'user'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Vote_v2": {"name": "Vote_v2", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_v2_chatId_Chat_id_fk": {"name": "Vote_v2_chatId_Chat_id_fk", "tableFrom": "Vote_v2", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_v2_messageId_Message_v2_id_fk": {"name": "Vote_v2_messageId_Message_v2_id_fk", "tableFrom": "Vote_v2", "tableTo": "Message_v2", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_v2_chatId_messageId_pk": {"name": "Vote_v2_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}}, "public.Vote": {"name": "Vote", "schema": "", "columns": {"chatId": {"name": "chatId", "type": "uuid", "primaryKey": false, "notNull": true}, "messageId": {"name": "messageId", "type": "uuid", "primaryKey": false, "notNull": true}, "isUpvoted": {"name": "isUpvoted", "type": "boolean", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Vote_chatId_Chat_id_fk": {"name": "Vote_chatId_Chat_id_fk", "tableFrom": "Vote", "tableTo": "Cha<PERSON>", "columnsFrom": ["chatId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Vote_messageId_Message_id_fk": {"name": "Vote_messageId_Message_id_fk", "tableFrom": "Vote", "tableTo": "Message", "columnsFrom": ["messageId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Vote_chatId_messageId_pk": {"name": "Vote_chatId_messageId_pk", "columns": ["chatId", "messageId"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}